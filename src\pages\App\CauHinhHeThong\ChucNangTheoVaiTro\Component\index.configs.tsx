import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};

export interface IFormChiTietChucNangTheoVaiTroFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;

  stt: IFormInput;
  trang_thai: IFormInput;
}
const FormChiTietChucNangTheoVaiTro: IFormChiTietChucNangTheoVaiTroFieldsConfig = {
  ma: {
    component: "input",
    name: "ma",
    label: "Mã vai trò",
    placeholder: "Mã vai trò",
    rules: [ruleRequired],
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên vai trò",
    placeholder: "Tên vai trò",
    rules: [ruleRequired],
  },

  stt: {
    component: "input",
    name: "stt",
    label: "Thứ tự hiển thị",
    placeholder: "Thứ tự hiển thị",
    rules: [ruleRequired],
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    rules: [ruleRequired],
  },
};
export const TRANG_THAI = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngưng sử dụng", ma: "K"},
];

export default FormChiTietChucNangTheoVaiTro;
export interface Props {}

export interface IModalChiTietChucNangTheoVaiTroRef {
  open: (data?: CommonExecute.Execute.IChiTietChucNangTheoVaiTro) => void;
  close: () => void;
}
//bảng danh sách chức năng chưa xác định
export interface TableChucNangChuaXacDinhDataType {
  key: string;
  stt?: number;
  ma?: string;
  ten?: string;
  loai?: string;
  is_checked?: string;
}
export const chucNangChuaXacDinhColumns: TableProps<TableChucNangChuaXacDinhDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott},
  {title: "Mã chức năng", dataIndex: "ma", key: "ma", width: 150, ...defaultTableColumnsProps},
  {title: "Tên chức năng", dataIndex: "ten", key: "ten", ...defaultTableColumnsProps},
  {title: "Loại", dataIndex: "loai", key: "loai", ...defaultTableColumnsProps},
  {title: "Chọn", dataIndex: "is_checked", key: "is_checked", width: 60, ...defaultTableColumnsProps},
];

export type DataIndexChuaXacDinh = keyof TableChucNangChuaXacDinhDataType;
//form tìm kiếm chức năng chưa xác định
export interface IFormTimKiemChucNangChuaXacDinhFieldsConfig {
  nd_tim: IFormInput;
}
export const FormTimKiemChucNangChuaXacDinh: IFormTimKiemChucNangChuaXacDinhFieldsConfig = {
  nd_tim: {
    component: "input",
    name: "nd_tim",
    label: "Tìm kiếm",
    placeholder: "Tìm kiếm",
  },
};
//bảng danh sách chức năng có cột xóa
export interface TableChucNangDataType {
  key: string;
  stt?: number;
  ma?: string;
  ten?: string;
  loai?: string;
  hanh_dong?: () => JSX.Element | null;
}
export const chucNangColumns: TableProps<TableChucNangDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott},
  {title: "Mã chức năng", dataIndex: "ma", key: "ma", width: 150, ...defaultTableColumnsProps},
  {title: "Tên chức năng", dataIndex: "ten", key: "ten", ...defaultTableColumnsProps},
  {title: "Loại", dataIndex: "loai", key: "loai", ...defaultTableColumnsProps},
  {title: "Hành động", dataIndex: "hanh_dong", key: "hanh_dong", width: 60, ...defaultTableColumnsProps},
];

export type DataIndexChucNang = keyof TableChucNangDataType;
